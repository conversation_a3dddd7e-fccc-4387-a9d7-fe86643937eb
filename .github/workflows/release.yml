name: Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., v1.0.0)'
        required: true
        default: 'v1.0.0'

permissions:
  contents: write

jobs:
  build-and-release:
    runs-on: windows-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pyinstaller
        
    - name: Build executable
      run: |
        pyinstaller --onefile --windowed --name AugmentJSS main.py
        New-Item -ItemType Directory -Force -Path release\binary
        Copy-Item dist\AugmentJSS.exe release\binary\AugmentJSS-${{ github.ref_name }}-windows-x64.exe
        
    - name: Get version
      id: get_version
      run: |
        if ("${{ github.event_name }}" -eq "workflow_dispatch") {
          $version = "${{ github.event.inputs.version }}"
        } else {
          $version = "${{ github.ref_name }}"
        }
        echo "version=$version" >> $env:GITHUB_OUTPUT
        
    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        tag_name: ${{ steps.get_version.outputs.version }}
        name: AugmentJSS ${{ steps.get_version.outputs.version }}
        body: |
          ## 🎯 AugmentJSS ${{ steps.get_version.outputs.version }} - 优化版本

          ### ✨ 主要改进
          - 优化项目文档结构，移除冗余内容
          - 简化安装和使用说明
          - 清理项目文件，保持仓库整洁
          - 重新打包优化可执行文件

          ### 📥 下载
          - **Windows 64位**: 下载 `AugmentJSS.exe` 直接运行
          - **源代码**: 下载源码包自行编译

          ### 🚀 功能特性
          - 📧 临时邮箱生成
          - 📬 验证码自动获取
          - 🔧 机器码重置功能

          ### 📋 系统要求
          - Windows 10/11 (64位)
          - 管理员权限（机器码重置功能）
        files: |
          release/binary/AugmentJSS-${{ steps.get_version.outputs.version }}-windows-x64.exe
        draft: false
        prerelease: false
        

