# 📥 安装指南

## 🚀 快速开始

### 方式一：下载可执行文件（推荐）

这是最简单的使用方式，无需安装Python环境。

1. **下载程序**
   - 访问 [Releases页面](../../releases)
   - 下载最新版本的 `AugmentJSS.exe`

2. **运行程序**
   - 双击 `AugmentJSS.exe` 即可运行
   - 首次运行可能需要几秒钟加载时间

3. **管理员权限**（可选）
   - 如需使用机器码重置功能，请右键选择"以管理员身份运行"

### 方式二：从源码运行

适合开发者或需要自定义的用户。

#### 环境要求
- Python 3.10 或更高版本
- Windows 10/11 (64位)

#### 安装步骤

1. **克隆仓库**
   ```bash
   git clone https://github.com/Han5111255am/Augmentjss.git
   cd Augmentjss
   ```

2. **创建虚拟环境**（推荐）
   ```bash
   python -m venv venv
   venv\Scripts\activate  # Windows
   ```

3. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

4. **运行程序**
   ```bash
   python main.py
   ```

## 🛠️ 故障排除

### 常见问题

#### 1. 程序无法启动
**症状**: 双击exe文件没有反应或闪退

**解决方案**:
- 检查是否有杀毒软件阻止
- 尝试以管理员身份运行
- 检查Windows事件查看器中的错误信息

#### 2. tkinter错误
**症状**: 出现"No module named 'tkinter'"错误

**解决方案**:
- 下载最新版本的可执行文件
- 或使用包含完整tkinter的Python环境重新打包

#### 3. 网络连接问题
**症状**: 邮箱生成或验证码获取失败

**解决方案**:
- 检查网络连接
- 检查防火墙设置
- 尝试更换网络环境

#### 4. 权限问题
**症状**: 机器码重置功能无法使用

**解决方案**:
- 右键选择"以管理员身份运行"
- 确保UAC（用户账户控制）已启用
- 检查用户是否具有管理员权限

### 系统兼容性

#### 支持的系统
- ✅ Windows 10 (64位)
- ✅ Windows 11 (64位)

#### 最低配置要求
- **CPU**: 任何64位处理器
- **内存**: 512MB可用内存
- **存储**: 100MB可用空间
- **网络**: 稳定的互联网连接

## 🔒 安全说明

### 杀毒软件误报
由于程序包含系统级功能，某些杀毒软件可能会误报。这是正常现象，您可以：

1. **添加到白名单**: 将程序添加到杀毒软件的白名单中
2. **临时禁用**: 运行时临时禁用实时保护
3. **信任来源**: 从官方GitHub仓库下载确保安全性

### 数据安全
- 程序不会收集或上传任何个人信息
- 所有网络请求仅用于邮箱服务API调用
- 机器码重置功能仅在本地执行

## 📞 获取帮助

如果您在安装或使用过程中遇到问题：

1. **查看文档**: 首先查看README.md和本安装指南
2. **搜索Issues**: 在GitHub Issues中搜索类似问题
3. **提交Issue**: 如果问题未解决，请提交新的Issue
4. **联系开发者**: 通过邮件或GitHub联系

### 提交Issue时请包含
- 操作系统版本
- Python版本（如果从源码运行）
- 错误信息的完整截图
- 复现步骤的详细描述

---

**💡 提示**: 建议首次使用时先阅读README.md了解功能特性，然后按照本指南进行安装。
